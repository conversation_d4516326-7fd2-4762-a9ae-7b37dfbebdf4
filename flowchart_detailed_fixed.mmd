```mermaid
graph TD
    subgraph MainProcess
        A[Start] --> B{Welcome Screen}
        B --> C{Have an account?}
        C -- Yes --> D[Login Process]
        C -- No --> E[Registration Process]

        D -- Success --> F[Show Main Menu]
        D -- Failure/3 attempts --> C

        E -- Success --> D
        E -- Failure/Cancel --> C

        F --> G{Check User Role}
        G -- Admin --> AdminMenu[Admin Menu]
        G -- Student --> StudentMenu[Student Menu]
        G -- Restaurant --> RestaurantMenu[Restaurant Menu]
        G -- Delivery Personnel --> DeliveryMenu[Delivery Personnel Menu]
    end

    subgraph AdminFunctions
        AdminMenu --> H1[User Management]
        AdminMenu --> H2[Reporting & Analytics]
        AdminMenu --> H3[Data Backup/Restore]
        AdminMenu --> Logout

        H1 --> H1a[View All Users]
        H1 --> H1b[Activate/Deactivate Accounts]
        H1 --> H1c[Manage User Details]
        H1c --> H1c1[Add/Update/Delete User]

        H2 --> H2a[Read Order/User data]
        H2a --> H2b[Calculate System Statistics]
        H2b --> H2c[Display Report]

        H3 --> H3a[Backup Data]
        H3 --> H3b[Restore Data]
    end

    subgraph StudentFunctions
        StudentMenu --> I1[Profile Management]
        StudentMenu --> I2[Place Order]
        StudentMenu --> I3[Track Order History]
        StudentMenu --> I4[Manage Account Balance]
        StudentMenu --> Logout

        I2 --> I2a[Display Restaurants]
        I2a --> I2b{Select Restaurant}
        I2b --> I2c[Display Menu Items]
        I2c --> I2d[Add Items to Cart]
        I2d --> I2e[Confirm Order]
        I2e --> I2f{Check Balance}
        I2f -- Sufficient --> I2g[Deduct Balance & Create Order]
        I2g --> I2h[Write to Order.txt & Queue.txt]
        I2f -- Insufficient --> I2i[Show Error & Return to Menu]

        I3 --> I3a[Read Order.txt & Delivery.txt]
        I3a --> I3b[Filter by Student ID]
        I3b --> I3c[Display Order Status]
    end

    subgraph RestaurantFunctions
        RestaurantMenu --> J1[Menu Item Management]
        RestaurantMenu --> J2[Order Queue Management]
        RestaurantMenu --> J3[Daily Sales Report]
        RestaurantMenu --> Logout

        J1 --> J1a[View/Add/Update/Delete Menu Items]
        J1a --> J1b[Modify Menu Item.txt]

        J2 --> J2a[Display Pending Orders from Queue.txt]
        J2a --> J2b{Select Order}
        J2b --> J2c{Update Status}
        J2c --> J2d[Update Order.txt & Queue.txt]

        J3 --> J3a[Read Order.txt]
        J3a --> J3b[Filter by Restaurant & Date]
        J3b --> J3c[Calculate Sales & Display Report]
    end

    subgraph DeliveryPersonnelFunctions
        DeliveryMenu --> K1[Order Assignment]
        DeliveryMenu --> K2[Manage Delivery Status]
        DeliveryMenu --> K3[Earnings & Performance]
        DeliveryMenu --> Logout

        K1 --> K1a[View Available Orders]
        K1a --> K1b{Accept Order}
        K1b -- Yes --> K1c[Check if already on delivery]
        K1c -- No --> K1d[Create Delivery Record]
        K1d --> K1e[Update Order Status to DELIVERING]
        K1e --> K1f[Update Personnel Status to ON_DELIVERY]

        K2 --> K2a[View Assigned Delivery]
        K2a --> K2b{Update Status to DELIVERED}
        K2b -- Yes --> K2c[Update Delivery.txt with Delivered Time]
        K2c --> K2d[Update Order Status to COMPLETED]
        K2d --> K2e[Update Personnel Status to AVAILABLE]
    end

    Logout --> C
```
