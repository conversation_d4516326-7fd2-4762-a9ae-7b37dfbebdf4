## Campus Restaurant System Pseudocode

### 1. Data Structures (Based on .h files)

```
// Represents any user in the system
STRUCTURE User
    id: STRING
    role: ENU<PERSON> (ADMIN, STUDENT, RESTAURANT, DELIVERY_PERSONNEL)
    name: STRING
    contact_number: STRING
    email_address: STRING
    password: STRING
END STRUCTURE

// Represents a Student user
STRUCTURE Student
    user_info: User
    student_id: STRING
    account_status: ENUM (ACTIVE, INACTIVE)
    account_balance: FLOAT
END STRUCTURE

// Represents a Restaurant user
STRUCTURE Restaurant
    user_info: User
    restaurant_id: STRING
    cuisine: ENUM (CHINESE, WESTERN, etc.)
END STRUCTURE

// Represents a menu item for a restaurant
STRUCTURE MenuItem
    restaurant_id: STRING
    item_id: STRING
    name: STRING
    price: FLOAT
END STRUCTURE

// Represents a Delivery Personnel user
STRUCTURE DeliveryPersonnel
    user_info: User
    delivery_personnel_id: STRING
    status: ENUM (AVAILABLE, ON_DELIVERY)
    current_delivery_id: STRING
END STRUCTURE

// Represents a single order placed by a student
STRUCTURE Order
    order_id: STRING
    student_id: STRING
    restaurant_id: STRING
    total_price: FLOAT
    order_date: STRING
    order_time: STRING
    status: STRING (e.g., "PREPARING", "READY", "DELIVERING", "COMPLETED", "CANCELLED")
    // Contains details of items within the order
    items: LIST OF OrderItem
END STRUCTURE

// Represents an item within an order
STRUCTURE OrderItem
    menu_item_id: STRING
    item_name: STRING
    quantity: INTEGER
    subtotal: FLOAT
END STRUCTURE

// Represents a delivery task
STRUCTURE Delivery
    delivery_id: STRING
    order_id: STRING
    delivery_date: STRING
    estimated_arrival_time: STRING
    delivered_time: STRING
    delivery_status: ENUM (IN_PROGRESS, DELIVERED)
END STRUCTURE

// Represents an order in the restaurant's queue
STRUCTURE QueueOrder
    order_id: STRING
    student_id: STRING
    order_time: STRING
    status: STRING
    items_summary: STRING
END STRUCTURE
```

### 2. Main Program Flow (`main.c`)

```
PROCEDURE Main()
    CREATE user_object AS User

    PRINT "Welcome to campus ordering system!"
    
    LOOP indefinitely
        LOOP 3 times (for attempts)
            PROMPT "Do you have an account? (1. Yes / 2. No)"
            GET user_choice

            IF user_choice is YES THEN
                IF Login(user_object) is SUCCESSFUL THEN
                    result = ShowMainMenu(user_object)
                    IF result is LOGOUT THEN
                        BREAK inner loop and start over
                    ELSE
                        EXIT program
                    END IF
                END IF
            ELSE IF user_choice is NO THEN
                IF Registration(user_object) is SUCCESSFUL THEN
                    IF Login(user_object) is SUCCESSFUL THEN
                        result = ShowMainMenu(user_object)
                        IF result is LOGOUT THEN
                            BREAK inner loop and start over
                        ELSE
                            EXIT program
                        END IF
                    END IF
                END IF
            ELSE
                PRINT "Invalid input!"
            END IF
        END LOOP
        PRINT "Too many invalid attempts."
    END LOOP
END PROCEDURE
```

### 3.2.1 Login

```
FUNCTION Login(user_object)
    PRINT "----- USER LOGIN -----"
    LOOP 3 times
        PROMPT for email and password
        
        IF FindUserByCredentials(email, password, user_object) is SUCCESSFUL THEN
            // Load role-specific ID (e.g., student_id) into a global variable
            // based on user_object.role
            LoadRoleSpecificID(user_object)
            RETURN SUCCESS
        END IF
    END LOOP
    PRINT "Too many failed login attempts."
    RETURN FAILURE
END FUNCTION
```

### 3.2.2 User Registration

```
FUNCTION Registration(user_object)
    PRINT "----- USER REGISTRATION -----"
    PROMPT "Register as: 1. Student, 2. Restaurant, 3. Delivery Personnel"
    GET role_choice

    CASE role_choice OF
        STUDENT:
            RETURN RegisterStudent(user_object)
        RESTAURANT:
            RETURN RegisterRestaurant(user_object)
        DELIVERY_PERSONNEL:
            RETURN RegisterDeliveryPersonnel(user_object)
        OTHERWISE:
            PRINT "Invalid choice."
            RETURN FAILURE
    END CASE
END FUNCTION

PROCEDURE RegisterStudent(user_object)
    // Prompt for name, contact, email, password
    CollectBasicUserInfo(user_object)
    // Confirm details
    // Generate new Student ID and User ID
    GenerateNewID("Student", student_id)
    GenerateNewID("User", user_id)
    // Set default values (balance=0, status=INACTIVE)
    // Write to Student.txt and User.txt
    WriteToFile("Student.txt", student_data)
    WriteToFile("User.txt", user_data)
END PROCEDURE

// RegisterRestaurant and RegisterDeliveryPersonnel follow a similar pattern
// with role-specific data collection (e.g., cuisine for restaurant).
```

### 4. Role-Based Menus (`login.c`)

```
FUNCTION ShowMainMenu(user_object)
    PRINT "Welcome back, [user_object.name]!"
    
    CASE user_object.role OF
        ADMIN:
            RunMenu(AdminMenu)
        STUDENT:
            RunMenu(StudentMenu)
        RESTAURANT:
            RunMenu(RestaurantMenu)
        DELIVERY_PERSONNEL:
            RunMenu(DeliveryPersonnelMenu)
    END CASE
END FUNCTION

PROCEDURE RunMenu(menu_options)
    LOOP indefinitely
        PRINT menu options
        PROMPT for choice
        
        IF choice is Logout THEN
            RETURN LOGOUT
        ELSE
            EXECUTE corresponding function for the choice
        END IF
    END LOOP
END PROCEDURE
```

### 3.2.3 Admin
```
PROCEDURE UserManagement()
    // Menu: 1. View all users, 2. Activate/Deactivate accounts, 3. Manage user details
    // 1. View all users:
    //    - Read User.txt
    //    - Print details for each user.
    // 2. Activate/Deactivate accounts:
    //    - Display all students from Student.txt with their status.
    //    - Prompt for Student ID to change.
    //    - Read Student.txt, find the student, rewrite the file with the new status.
    // 3. Manage user details:
    //    - Add/Update/Delete user accounts, which involves modifying
    //      User.txt and the corresponding role file (Student.txt, etc.).
END PROCEDURE

PROCEDURE ReportingAndAnalytics()
    // Read data from Order.txt, User.txt, Delivery.txt etc.
    // Calculate statistics:
    //  - Total users, orders, revenue
    //  - Average order value, delivery time
    //  - Peak hours
    // Display formatted report.
END PROCEDURE

PROCEDURE DataBackupAndRestore()
    // Menu: 1. Backup, 2. Restore
    // 1. Backup:
    //    - Create a timestamped backup folder.
    //    - Copy all .txt data files into the backup folder.
    // 2. Restore:
    //    - List available backups.
    //    - Prompt user to select a backup.
    //    - Copy files from the selected backup folder to the main directory.
END PROCEDURE
```

### 3.2.4 Student
```
PROCEDURE ProfileManagement()
    // Menu: 1. View, 2. Update, 3. Delete
    // View: Read and display current student's info from Student.txt.
    // Update: Prompt for which field to change (name, contact, etc.),
    //         then rewrite the student's line in Student.txt and User.txt.
    // Delete: Remove the student's records from Student.txt and User.txt after confirmation.
END PROCEDURE

PROCEDURE OrderPlacement()
    // 1. Display list of restaurants from Restaurant.txt.
    // 2. User selects a restaurant.
    // 3. Display menu for that restaurant from Menu Item.txt.
    // 4. User adds items to a cart (temporary list of OrderItem).
    // 5. Calculate total price.
    // 6. Check if student's account_balance is sufficient.
    // 7. Confirm order.
    // 8. Deduct total price from student's balance in Student.txt.
    // 9. Generate new Order ID.
    // 10. Write order details to Order.txt and Queue.txt.
END PROCEDURE

PROCEDURE TrackOrderHistory()
    // Read Order.txt and Delivery.txt.
    // Find all orders matching the current student's ID.
    // Display a formatted list of past and current orders with their status.
END PROCEDURE

PROCEDURE AccountBalanceManagement()
    // 1. View current balance from Student.txt.
    // 2. Prompt for top-up amount.
    // 3. Add amount to student's balance in Student.txt.
END PROCEDURE
```

### 3.2.5 Restaurant
```
PROCEDURE MenuItemManagement()
    // Menu: 1. View, 2. Add, 3. Update, 4. Delete
    // All operations filter Menu Item.txt by the current restaurant's ID.
    // Add: Prompt for item name and price, generate new Menu Item ID, append to file.
    // Update/Delete: Display items, user selects one, then rewrite/remove from file.
END PROCEDURE

PROCEDURE OrderQueueManagement()
    // 1. Display all orders for the restaurant from Queue.txt that are not "READY".
    // 2. User selects an order to update.
    // 3. User selects a new status (e.g., "PREPARING" -> "READY").
    // 4. Update the status in both Order.txt and Queue.txt.
    //    If status becomes "READY", it's available for delivery personnel.
END PROCEDURE

PROCEDURE DailySalesReport()
    // Read Order.txt.
    // Filter orders for the current restaurant and today's date.
    // Calculate total sales, number of orders, best-selling item, etc.
    // Display the report.
END PROCEDURE

PROCEDURE InventoryTracking()
    // Basic CRUD operations for inventory items.
    // Read/write from Inventory.txt, filtered by the restaurant's ID.
END PROCEDURE
```

### 3.2.6 Delivery Personnel
```
PROCEDURE OrderAssignment()
    // Menu: 1. View available orders, 2. Accept order
    // 1. View available orders:
    //    - Read Order.txt for orders with "READY" status.
    //    - Display them in a list.
    // 2. Accept order:
    //    - User selects an order from the available list.
    //    - Check if personnel is already on a delivery. If not:
    //    - Generate new Delivery ID.
    //    - Create a new record in Delivery.txt.
    //    - Update the order's status to "DELIVERING" in Order.txt.
    //    - Update the personnel's status to "ON_DELIVERY" in Delivery Personnel.txt.
END PROCEDURE

PROCEDURE DeliveryStatusManagement()
    // 1. View current assigned delivery details (from Delivery.txt).
    // 2. Prompt to update status to "DELIVERED".
    // 3. If updated:
    //    - Update the delivery record in Delivery.txt (add delivered_time).
    //    - Update the order status to "COMPLETED" in Order.txt.
    //    - Update the personnel's status to "AVAILABLE" in Delivery Personnel.txt.
END PROCEDURE

PROCEDURE EarningsAndPerformance()
    // Read Delivery.txt for all completed deliveries by the current personnel.
    // Calculate total earnings (based on a fixed fee per delivery or other logic).
    // Calculate performance stats (e.g., average delivery time, on-time rate).
    // Display the summary.
END PROCEDURE
```

### 9. Database/File Operations (Implicit in all `.c` files)

```
FUNCTION FindUserByCredentials(email, password, user_object)
    OPEN User.txt for reading
    LOOP through each line
        PARSE line into user data
        IF parsed_email matches email AND parsed_password matches password THEN
            COPY parsed data to user_object
            CLOSE file
            RETURN SUCCESS
        END IF
    END LOOP
    CLOSE file
    RETURN FAILURE
END FUNCTION

// Generic "WriteToFile" and "UpdateFile" procedures are used throughout the code.
// They typically involve:
// 1. Reading a file line-by-line into a temporary file.
// 2. When the target line is found, writing the new/updated data instead of the old line.
// 3. For additions, simply appending to the end of the file.
// 4. For deletions, skipping the line when writing to the temporary file.
// 5. Finally, deleting the original file and renaming the temporary file.
```
