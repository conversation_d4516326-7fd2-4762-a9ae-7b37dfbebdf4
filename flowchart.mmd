graph TD
    A[Campus Restaurant System] --> B["3.1.1 Login"];
    A --> C["3.1.2 User Registration"];
    A --> D["3.1.3 Admin"];
    A --> E["3.1.4 Student"];
    A --> F["3.1.5 Restaurant"];
    A --> G["3.1.6 Delivery Personnel"];

    subgraph Admin Functions
        D --> D1[User Management];
        D --> D2[Reporting & Analytics];
        D --> D3[Data Backup/Restore];
    end

    subgraph Student Functions
        E --> E1[Profile Management];
        E --> E2[Place Order];
        E --> E3[Track Order History];
        E --> E4[Manage Account Balance];
    end

    subgraph Restaurant Functions
        F --> F1[Menu Item Management];
        F --> F2[Order Queue Management];
        F --> F3[Daily Sales Report];
    end

    subgraph Delivery Personnel Functions
        G --> G1[Order Assignment];
        G --> G2[Manage Delivery Status];
        G --> G3[Earnings & Performance];
    end